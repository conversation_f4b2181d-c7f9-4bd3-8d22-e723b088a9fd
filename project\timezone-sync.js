/**
 * نظام تزامن المناطق الزمنية
 * يضمن تطابق الساعات ومواقيت الصلاة مع المدينة المختارة
 * نسخة 1.0
 */

const TimezoneSync = {
    // المتغيرات
    currentCity: 'Asia/Amman',
    currentTimezone: 'Asia/Amman',
    isInitialized: false,

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام تزامن المناطق الزمنية...');
        
        try {
            // تحميل المدينة المحفوظة
            this.loadSavedCity();
            
            // إعداد مستمعات الأحداث
            this.setupEventListeners();
            
            // تحديث فوري
            this.syncAll();
            
            this.isInitialized = true;
            console.log('تم تهيئة نظام تزامن المناطق الزمنية بنجاح');
            
        } catch (error) {
            console.error('خطأ في تهيئة نظام تزامن المناطق الزمنية:', error);
        }
    },

    // تحميل المدينة المحفوظة
    loadSavedCity: function() {
        try {
            const savedCity = localStorage.getItem('selectedCity');
            if (savedCity) {
                this.currentCity = savedCity;
                this.currentTimezone = this.getCityTimezone(savedCity);
                console.log(`تم تحميل المدينة المحفوظة: ${savedCity} (${this.currentTimezone})`);
            }
        } catch (error) {
            console.error('خطأ في تحميل المدينة المحفوظة:', error);
        }
    },

    // الحصول على المنطقة الزمنية للمدينة
    getCityTimezone: function(cityKey) {
        try {
            // إذا كان cityKey يحتوي على منطقة زمنية مباشرة
            if (cityKey && cityKey.includes('/')) {
                return cityKey;
            }
            
            // البحث في قاعدة البيانات
            if (typeof CITIES_DATABASE !== 'undefined' && CITIES_DATABASE[cityKey]) {
                const cityData = CITIES_DATABASE[cityKey];
                if (cityData.timezone) {
                    if (typeof cityData.timezone === 'string') {
                        return cityData.timezone;
                    } else {
                        // تحويل offset إلى منطقة زمنية
                        return this.getTimezoneFromOffset(cityData.timezone);
                    }
                }
            }
            
            // القيمة الافتراضية
            return 'Asia/Amman';
        } catch (error) {
            console.error('خطأ في الحصول على المنطقة الزمنية:', error);
            return 'Asia/Amman';
        }
    },

    // تحويل offset إلى منطقة زمنية
    getTimezoneFromOffset: function(offset) {
        const timezoneMap = {
            3: 'Asia/Amman',      // الأردن، فلسطين، لبنان
            4: 'Asia/Dubai',      // الإمارات، عُمان
            3: 'Asia/Riyadh',     // السعودية
            2: 'Africa/Cairo',    // مصر
            1: 'Africa/Tunis',    // تونس، الجزائر
            0: 'Europe/London',   // المغرب (GMT)
            5: 'Asia/Karachi',    // باكستان
            6: 'Asia/Dhaka'       // بنغلاديش
        };
        
        return timezoneMap[offset] || 'Asia/Amman';
    },

    // تحديث المدينة والمنطقة الزمنية
    updateCity: function(cityKey) {
        try {
            if (!cityKey) return false;
            
            const oldCity = this.currentCity;
            const oldTimezone = this.currentTimezone;
            
            this.currentCity = cityKey;
            this.currentTimezone = this.getCityTimezone(cityKey);
            
            console.log(`تحديث المدينة من ${oldCity} إلى ${cityKey}`);
            console.log(`تحديث المنطقة الزمنية من ${oldTimezone} إلى ${this.currentTimezone}`);
            
            // حفظ المدينة الجديدة
            localStorage.setItem('selectedCity', cityKey);
            
            // تزامن جميع الأنظمة
            this.syncAll();
            
            // إطلاق الأحداث
            this.dispatchEvents();
            
            return true;
        } catch (error) {
            console.error('خطأ في تحديث المدينة:', error);
            return false;
        }
    },

    // تزامن جميع الأنظمة
    syncAll: function() {
        try {
            console.log(`تزامن جميع الأنظمة مع المدينة: ${this.currentCity} (${this.currentTimezone})`);
            
            // تحديث المنطقة الزمنية في نظام الساعة
            this.syncClockSystem();
            
            // تحديث مواقيت الصلاة
            this.syncPrayerTimes();
            
            // تحديث الساعات
            this.syncClocks();
            
        } catch (error) {
            console.error('خطأ في تزامن الأنظمة:', error);
        }
    },

    // تزامن نظام الساعة
    syncClockSystem: function() {
        try {
            if (typeof updateCurrentTimezone === 'function') {
                updateCurrentTimezone(this.currentCity);
            }
        } catch (error) {
            console.error('خطأ في تزامن نظام الساعة:', error);
        }
    },

    // تزامن مواقيت الصلاة
    syncPrayerTimes: function() {
        try {
            if (typeof PrayerTimesManager !== 'undefined') {
                // تحديث مواقيت الصلاة للمدينة الجديدة
                const currentDate = new Date();
                PrayerTimesManager.updatePrayerTimesWithAdjustments(this.currentCity, currentDate);
            }
        } catch (error) {
            console.error('خطأ في تزامن مواقيت الصلاة:', error);
        }
    },

    // تزامن الساعات
    syncClocks: function() {
        try {
            // تحديث الساعة التناظرية
            if (typeof updateAnalogClock === 'function') {
                updateAnalogClock();
            }
            
            // تحديث الساعة الرقمية
            if (typeof updateDigitalClock === 'function') {
                updateDigitalClock();
            }
            
            // تحديث التاريخ
            if (typeof updateDate === 'function') {
                updateDate();
            }
        } catch (error) {
            console.error('خطأ في تزامن الساعات:', error);
        }
    },

    // إطلاق الأحداث
    dispatchEvents: function() {
        try {
            // حدث تغيير المدينة
            if (typeof window !== 'undefined' && window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('cityChanged', {
                    detail: { 
                        city: this.currentCity, 
                        timezone: this.currentTimezone 
                    }
                }));
                
                window.dispatchEvent(new CustomEvent('timezoneChanged', {
                    detail: { 
                        timezone: this.currentTimezone,
                        cityKey: this.currentCity
                    }
                }));
            }
        } catch (error) {
            console.error('خطأ في إطلاق الأحداث:', error);
        }
    },

    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        try {
            // لا نحتاج لمستمعات إضافية هنا لأن المستمعات موجودة في index.html
            // فقط مستمع تغيير الدولة
            const countrySelect = document.getElementById('country-select');
            if (countrySelect) {
                countrySelect.addEventListener('change', () => {
                    // إعادة تعيين المدينة عند تغيير الدولة
                    setTimeout(() => {
                        const newCitySelect = document.getElementById('city-select');
                        const newSelectedCitySelect = document.getElementById('selected-city');

                        if (newCitySelect && newCitySelect.value) {
                            this.updateCity(newCitySelect.value);
                        } else if (newSelectedCitySelect && newSelectedCitySelect.value) {
                            this.updateCity(newSelectedCitySelect.value);
                        }
                    }, 100);
                });
            }

        } catch (error) {
            console.error('خطأ في إعداد مستمعات الأحداث:', error);
        }
    },

    // الحصول على الوقت حسب المنطقة الزمنية الحالية
    getCurrentTime: function() {
        try {
            const now = new Date();
            return new Date(now.toLocaleString("en-US", {timeZone: this.currentTimezone}));
        } catch (error) {
            console.error('خطأ في الحصول على الوقت الحالي:', error);
            return new Date();
        }
    },

    // الحصول على معلومات الحالة
    getStatus: function() {
        return {
            currentCity: this.currentCity,
            currentTimezone: this.currentTimezone,
            isInitialized: this.isInitialized,
            currentTime: this.getCurrentTime()
        };
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل جميع المكتبات
    setTimeout(() => {
        TimezoneSync.initialize();
    }, 800);
});

// تصدير الكائن
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimezoneSync;
}

// إتاحة الكائن عالمياً
window.TimezoneSync = TimezoneSync;
